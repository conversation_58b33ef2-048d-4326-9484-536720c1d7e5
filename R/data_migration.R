#' Data Migration Tools for fcmsafety
#'
#' Functions to migrate existing xlsx database files to the new SQLite format.
#'
#' @importFrom DBI dbWriteTable dbGetQuery dbExecute
#' @importFrom rio import
#' @importFrom dplyr mutate select rename filter
#' @importFrom stringr str_replace str_remove_all

#' Migrate XLSX Files to SQLite Database
#'
#' Migrates all existing xlsx database files to the new SQLite format.
#'
#' @param xlsx_dir Directory containing xlsx files (default: inst/)
#' @param db_path Path to SQLite database (default: inst/fcmsafety.db)
#' @param force_recreate Logical. Whether to recreate database if it exists
#'
#' @return Logical indicating success
#' @export
migrate_xlsx_to_sqlite <- function(xlsx_dir = NULL, db_path = NULL, force_recreate = FALSE) {
  if (is.null(xlsx_dir)) {
    xlsx_dir <- file.path(getwd(), "inst")
  }

  if (is.null(db_path)) {
    db_path <- file.path(getwd(), "inst", "fcmsafety.db")
  }

  # Initialize database
  if (!init_fcmsafety_database(db_path, force_recreate)) {
    stop("Failed to initialize database")
  }

  message("Starting migration of xlsx files to SQLite database...")

  # Migrate each database
  databases <- list(
    list(name = "svhc", file = "svhc.xlsx", migrate_func = migrate_svhc_data),
    list(name = "cmr", file = "cmr.xlsx", migrate_func = migrate_cmr_data),
    list(name = "suspect_cmr", file = "suspect_cmr.xlsx", migrate_func = migrate_suspect_cmr_data),
    list(name = "iarc", file = "iarc.xlsx", migrate_func = migrate_iarc_data),
    list(name = "eu_sml", file = "eu10_2011.xlsx", migrate_func = migrate_eu_sml_data),
    list(name = "eu_sml_group", file = "eu10_2011_group.xlsx", migrate_func = migrate_eu_sml_group_data),
    list(name = "edc", file = "edc.xlsx", migrate_func = migrate_edc_data),
    list(name = "china_sml", file = "china_sml_cleaned.xlsx", migrate_func = migrate_china_sml_data)
  )

  success_count <- 0

  for (db_info in databases) {
    xlsx_path <- file.path(xlsx_dir, db_info$file)

    if (file.exists(xlsx_path)) {
      message("Migrating ", db_info$name, " database...")

      tryCatch({
        result <- db_info$migrate_func(xlsx_path, db_path)
        if (result) {
          success_count <- success_count + 1
          message("✓ ", db_info$name, " migration completed")
        } else {
          message("✗ ", db_info$name, " migration failed")
        }
      }, error = function(e) {
        message("✗ ", db_info$name, " migration error: ", e$message)
      })
    } else {
      message("⚠ ", db_info$name, " file not found: ", xlsx_path)
    }
  }

  message("Migration completed. ", success_count, "/", length(databases), " databases migrated successfully.")

  # Update database versions
  conn <- get_db_connection(db_path)
  for (db_info in databases) {
    update_database_version(
      database_name = db_info$name,
      version = "1.0.0",
      notes = "Migrated from xlsx format"
    )
  }

  return(success_count == length(databases))
}

#' Migrate SVHC Data
#'
#' @param xlsx_path Path to SVHC xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_svhc_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in SVHC file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      substance_name = `Substance name`,
      ec_number = if ("EC number" %in% names(data)) `EC number` else NA,
      cas_no = if ("CAS No." %in% names(data)) `CAS No.` else NA,
      hazard_detailed_concern = if ("Hazard detailed concern" %in% names(data)) `Hazard detailed concern` else NA,
      date_inclusion = if ("Date inclusion" %in% names(data)) `Date inclusion` else NA,
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(substance_name, ec_number, cas_no, hazard_detailed_concern, date_inclusion,
           cid, inchikey, isomeric_smiles, molecular_formula, exact_mass,
           cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "svhc_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM svhc_data")$count
  update_database_version("svhc", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate CMR Data
#'
#' @param xlsx_path Path to CMR xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_cmr_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in CMR file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      index_no = `Index No`,
      international_chemical_identification = if ("International chemical identification" %in% names(data)) `International chemical identification` else NA,
      ec_no = if ("EC No" %in% names(data)) `EC No` else NA,
      cas_no = if ("CAS No" %in% names(data)) `CAS No` else NA,
      hazard_statement_codes = if ("Hazard Statement Code(s)" %in% names(data)) `Hazard Statement Code(s)` else NA,
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(index_no, international_chemical_identification, ec_no, cas_no,
           hazard_statement_codes, cid, inchikey, isomeric_smiles,
           molecular_formula, exact_mass, cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "cmr_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM cmr_data")$count
  update_database_version("cmr", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate Suspect CMR Data
#'
#' @param xlsx_path Path to suspect CMR xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_suspect_cmr_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in suspect CMR file")
    return(FALSE)
  }

  # Convert to database format (same structure as CMR)
  db_data <- data %>%
    mutate(
      index_no = `Index No`,
      international_chemical_identification = if ("International chemical identification" %in% names(data)) `International chemical identification` else NA,
      ec_no = if ("EC No" %in% names(data)) `EC No` else NA,
      cas_no = if ("CAS No" %in% names(data)) `CAS No` else NA,
      hazard_statement_codes = if ("Hazard Statement Code(s)" %in% names(data)) `Hazard Statement Code(s)` else NA,
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(index_no, international_chemical_identification, ec_no, cas_no,
           hazard_statement_codes, cid, inchikey, isomeric_smiles,
           molecular_formula, exact_mass, cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "suspect_cmr_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM suspect_cmr_data")$count
  update_database_version("suspect_cmr", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate IARC Data
#'
#' @param xlsx_path Path to IARC xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_iarc_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in IARC file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      cas_no = if ("CAS No." %in% names(data)) `CAS No.` else NA,
      agent = Agent,
      group_classification = if ("Group" %in% names(data)) Group else NA,
      volume = if ("Volume" %in% names(data)) Volume else NA,
      year = if ("Year" %in% names(data)) Year else NA,
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(cas_no, agent, group_classification, volume, year,
           cid, inchikey, isomeric_smiles, molecular_formula, exact_mass,
           cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "iarc_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM iarc_data")$count
  update_database_version("iarc", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate EU SML Data
#'
#' @param xlsx_path Path to EU SML xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_eu_sml_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in EU SML file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      fcm_substance_no = `FCM substance No`,
      substance_name = if ("Substance name" %in% names(data)) `Substance name` else NA,
      cas_no = if ("CAS No" %in% names(data)) `CAS No` else NA,
      restrictions_and_specifications = if ("Restrictions and/or specifications" %in% names(data)) `Restrictions and/or specifications` else NA,
      # Handle SML column with various possible names
      sml_mg_kg = case_when(
        "SML" %in% names(data) ~ SML,
        "SML\n                           [mg/kg]" %in% names(data) ~ `SML\n                           [mg/kg]`,
        TRUE ~ NA_real_
      ),
      sml_group = case_when(
        "SML_group" %in% names(data) ~ SML_group,
        "SML(T)\n                           [mg/kg]\n                           (Group restriction No)" %in% names(data) ~ `SML(T)\n                           [mg/kg]\n                           (Group restriction No)`,
        TRUE ~ NA_character_
      ),
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(fcm_substance_no, substance_name, cas_no, restrictions_and_specifications,
           sml_mg_kg, sml_group, cid, inchikey, isomeric_smiles,
           molecular_formula, exact_mass, cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "eu_sml_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM eu_sml_data")$count
  update_database_version("eu_sml", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate EU SML Group Data
#'
#' @param xlsx_path Path to EU SML group xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_eu_sml_group_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in EU SML group file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      group_restriction_no = `Group Restriction No`,
      restriction_description = if ("Restriction" %in% names(data)) Restriction else NA,
      sml_t_mg_kg = case_when(
        "SML" %in% names(data) ~ SML,
        "SML (T)\n                     [mg/kg]" %in% names(data) ~ `SML (T)\n                     [mg/kg]`,
        TRUE ~ NA_real_
      ),
      verification = if ("Verification" %in% names(data)) Verification else NA
    ) %>%
    select(group_restriction_no, restriction_description, sml_t_mg_kg, verification)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "eu_sml_group_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM eu_sml_group_data")$count
  update_database_version("eu_sml_group", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate EDC Data
#'
#' @param xlsx_path Path to EDC xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_edc_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in EDC file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      substance_name = if ("Substance name" %in% names(data)) `Substance name` else NA,
      cas_no = if ("CAS No" %in% names(data)) `CAS No` else NA,
      source_database = if ("Source" %in% names(data)) Source else NA,
      evidence_level = if ("Evidence" %in% names(data)) Evidence else NA,
      endpoint = if ("Endpoint" %in% names(data)) Endpoint else NA,
      notes = if ("Notes" %in% names(data)) Notes else NA,
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(substance_name, cas_no, source_database, evidence_level, endpoint, notes,
           cid, inchikey, isomeric_smiles, molecular_formula, exact_mass,
           cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "edc_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM edc_data")$count
  update_database_version("edc", "1.0.0", record_count = count)

  return(TRUE)
}

#' Migrate China SML Data
#'
#' @param xlsx_path Path to China SML xlsx file
#' @param db_path Path to SQLite database
#'
#' @return Logical indicating success
migrate_china_sml_data <- function(xlsx_path, db_path) {
  data <- rio::import(xlsx_path)

  if (nrow(data) == 0) {
    message("No data found in China SML file")
    return(FALSE)
  }

  # Convert to database format
  db_data <- data %>%
    mutate(
      fca_no = if ("FCA No" %in% names(data)) `FCA No` else NA,
      substance_name_cn = if ("Substance name (CN)" %in% names(data)) `Substance name (CN)` else NA,
      cas_no = if ("CAS" %in% names(data)) CAS else NA,
      substance_name_en = if ("Substance name (EN)" %in% names(data)) `Substance name (EN)` else NA,
      sml_mg_kg = if ("SML" %in% names(data)) SML else NA,
      sml_group = if ("SML_group" %in% names(data)) SML_group else NA,
      restrictions = if ("Restrictions" %in% names(data)) Restrictions else NA,
      notes = if ("Notes" %in% names(data)) Notes else NA,
      cid = if ("CID" %in% names(data)) CID else NA,
      inchikey = if ("InChIKey" %in% names(data)) InChIKey else NA,
      isomeric_smiles = if ("SMILES" %in% names(data)) SMILES else NA,
      molecular_formula = if ("MolecularFormula" %in% names(data)) MolecularFormula else NA,
      exact_mass = if ("ExactMass" %in% names(data)) ExactMass else NA,
      cas_retrieved = if ("CAS_retrieved" %in% names(data)) CAS_retrieved else NA,
      flavornet = if ("Flavornet" %in% names(data)) Flavornet else NA
    ) %>%
    select(fca_no, substance_name_cn, cas_no, substance_name_en, sml_mg_kg,
           sml_group, restrictions, notes, cid, inchikey, isomeric_smiles,
           molecular_formula, exact_mass, cas_retrieved, flavornet)

  # Write to database
  conn <- get_db_connection(db_path)
  DBI::dbWriteTable(conn, "china_sml_data", db_data, append = TRUE, row.names = FALSE)

  # Update record count
  count <- DBI::dbGetQuery(conn, "SELECT COUNT(*) as count FROM china_sml_data")$count
  update_database_version("china_sml", "1.0.0", record_count = count)

  return(TRUE)
}
