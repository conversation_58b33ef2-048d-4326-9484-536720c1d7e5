#' Data Access Layer for fcmsafety
#'
#' Functions to access chemical safety database data, replacing global variables
#' with proper database access methods.
#'
#' @importFrom DBI dbGetQuery
#' @importFrom dplyr filter select mutate rename
#' @importFrom stringr str_replace str_remove_all

# Cache for database data to improve performance
.fcmsafety_cache <- new.env()

#' Get SVHC Database Data
#'
#' Retrieves Substances of Very High Concern (SVHC) data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with SVHC data
#' @export
get_svhc_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "svhc_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid) {
      data <- data %>% filter(!is.na(inchikey) & inchikey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM svhc_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `Substance name` = substance_name,
        `EC number` = ec_number,
        `CAS No.` = cas_no,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Get CMR Database Data
#'
#' Retrieves Carcinogenic, Mutagenic, and Reprotoxic (CMR) data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with CMR data
#' @export
get_cmr_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "cmr_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid) {
      data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM cmr_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `Index No` = index_no,
        `International chemical identification` = international_chemical_identification,
        `EC No` = ec_no,
        `CAS No` = cas_no,
        `Hazard Statement Code(s)` = hazard_statement_codes,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Get Suspect CMR Database Data
#'
#' Retrieves suspect CMR data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with suspect CMR data
#' @export
get_suspect_cmr_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "suspect_cmr_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid) {
      data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM suspect_cmr_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `Index No` = index_no,
        `International chemical identification` = international_chemical_identification,
        `EC No` = ec_no,
        `CAS No` = cas_no,
        `Hazard Statement Code(s)` = hazard_statement_codes,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Get IARC Database Data
#'
#' Retrieves International Agency for Research on Cancer (IARC) data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with IARC data
#' @export
get_iarc_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "iarc_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid) {
      data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM iarc_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `CAS No.` = cas_no,
        Agent = agent,
        Group = group_classification,
        Volume = volume,
        Year = year,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Get EU SML Database Data
#'
#' Retrieves EU Specific Migration Limit (SML) data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with EU SML data
#' @export
get_eu_sml_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "eu_sml_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid) {
      data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM eu_sml_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names and process data to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `FCM substance No` = fcm_substance_no,
        `Substance name` = substance_name,
        `CAS No` = cas_no,
        SML = sml_mg_kg,
        SML_group = sml_group,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      ) %>%
      mutate(
        # Apply the same data processing as in the original load_databases function
        SML = case_when(
          is.na(SML) ~ NA_real_,
          TRUE ~ SML
        )
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Get EDC Database Data
#'
#' Retrieves Endocrine Disrupting Chemicals (EDC) data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with EDC data
#' @export
get_edc_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "edc_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid) {
      data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM edc_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `Substance name` = substance_name,
        `CAS No` = cas_no,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Get China SML Database Data
#'
#' Retrieves China GB 9685 SML data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#' @param filter_valid Logical. Whether to filter out records without InChIKey
#'
#' @return Data frame with China SML data
#' @export
get_china_sml_data <- function(use_cache = TRUE, filter_valid = TRUE) {
  cache_key <- "china_sml_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    data <- get(cache_key, envir = .fcmsafety_cache)
    if (filter_valid && "InChIKey" %in% names(data)) {
      data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
    }
    return(data)
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM china_sml_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `FCA No` = fca_no,
        `Substance name (CN)` = substance_name_cn,
        `CAS` = cas_no,
        `Substance name (EN)` = substance_name_en,
        SML = sml_mg_kg,
        SML_group = sml_group,
        InChIKey = inchikey,
        CID = cid,
        SMILES = isomeric_smiles,
        MolecularFormula = molecular_formula,
        ExactMass = exact_mass,
        CAS_retrieved = cas_retrieved,
        Flavornet = flavornet
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  if (filter_valid && "InChIKey" %in% names(data)) {
    data <- data %>% filter(!is.na(InChIKey) & InChIKey != "")
  }

  return(data)
}

#' Clear Data Cache
#'
#' Clears the cached database data to force fresh retrieval.
#'
#' @param database_name Optional. Name of specific database cache to clear
#'
#' @export
clear_data_cache <- function(database_name = NULL) {
  if (is.null(database_name)) {
    # Clear all cache
    rm(list = ls(envir = .fcmsafety_cache), envir = .fcmsafety_cache)
    message("All database cache cleared")
  } else {
    cache_key <- paste0(database_name, "_data")
    if (exists(cache_key, envir = .fcmsafety_cache)) {
      rm(list = cache_key, envir = .fcmsafety_cache)
      message("Cache cleared for: ", database_name)
    }
  }
}

#' Load Databases (New Implementation)
#'
#' Loads all chemical safety databases using the new data access layer.
#' This function maintains compatibility with the original load_databases function
#' while using the new SQLite backend.
#'
#' @param use_default Logical. If TRUE, uses package databases. If FALSE, uses local databases
#' @param use_cache Logical. Whether to use cached data
#'
#' @return Invisible. Loads data into global environment for backward compatibility
#' @export
load_databases_new <- function(use_default = TRUE, use_cache = TRUE) {
  # For backward compatibility, we still load into global environment
  # but now we use the new data access functions

  if (!use_default) {
    # Check if local database exists
    local_db_path <- file.path(getwd(), "inst", "fcmsafety.db")
    if (!file.exists(local_db_path)) {
      message("Local database not found. Please run migrate_xlsx_to_sqlite() first.")
      return(invisible(FALSE))
    }
  }

  # Load all databases using new access functions
  svhc <<- get_svhc_data(use_cache = use_cache)
  cmr <<- get_cmr_data(use_cache = use_cache)
  cmr_suspect <<- get_suspect_cmr_data(use_cache = use_cache)
  iarc <<- get_iarc_data(use_cache = use_cache)
  eu_sml <<- get_eu_sml_data(use_cache = use_cache)
  eu_sml_group <<- get_eu_sml_group_data(use_cache = use_cache)
  edc <<- get_edc_data(use_cache = use_cache)
  china_sml <<- get_china_sml_data(use_cache = use_cache)

  message("Databases loaded successfully using new SQLite backend")
  return(invisible(TRUE))
}

#' Get EU SML Group Database Data
#'
#' Retrieves EU SML group restriction data from the database.
#'
#' @param use_cache Logical. Whether to use cached data if available
#'
#' @return Data frame with EU SML group data
#' @export
get_eu_sml_group_data <- function(use_cache = TRUE) {
  cache_key <- "eu_sml_group_data"

  # Check cache first
  if (use_cache && exists(cache_key, envir = .fcmsafety_cache)) {
    return(get(cache_key, envir = .fcmsafety_cache))
  }

  # Get data from database
  conn <- get_db_connection()
  query <- "SELECT * FROM eu_sml_group_data"
  data <- DBI::dbGetQuery(conn, query)

  # Convert column names to match original format
  if (nrow(data) > 0) {
    data <- data %>%
      rename(
        `Group Restriction No` = group_restriction_no,
        SML = sml_t_mg_kg
      )
  }

  # Cache the data
  if (use_cache) {
    assign(cache_key, data, envir = .fcmsafety_cache)
  }

  return(data)
}
