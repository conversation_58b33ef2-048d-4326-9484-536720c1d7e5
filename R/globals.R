utils::globalVariables(c(
  ".", "CID", "value", "Hazard Statement Code(s)", "<<-",
  "FCM substance No", "Group Restriction No", "InChIKey",
  "IsomericSMILES", "NAME", "CAS", "SMILES", "iarc_meta",
  "Flavornet", "CAS_retrieved", "ExactMass", "Cramer_rules",
  "Toxic_level",
  "eu_sml_meta", "china_sml_meta", "SML", "SML_group",
  "SML\r\n                     [mg/kg]",
  "SML (T)\r\n                     [mg/kg]",
  "SML(T)\r\n                     [mg/kg]\r\n                     (Group restriction No)",
  "Formula", "ExactMass", "MolecularFormula",
  "CAS No",
  "Substance name",
  "Index No",
  # New database column names
  "substance_name", "cas_no", "inchikey", "cid", "isomeric_smiles",
  "molecular_formula", "exact_mass", "cas_retrieved", "flavornet",
  "index_no", "international_chemical_identification", "ec_no",
  "hazard_statement_codes", "agent", "group_classification",
  "fcm_substance_no", "sml_mg_kg", "sml_group", "group_restriction_no",
  "sml_t_mg_kg", "fca_no", "substance_name_cn", "substance_name_en",
  # Additional column names from xlsx files
  "EC number", "Hazard detailed concern", "Date inclusion",
  "International chemical identification", "EC No", "CAS No",
  "Agent", "Group", "Volume", "Year", "Substance name", "CAS No.",
  "Restrictions and/or specifications", "SML\n                           [mg/kg]",
  "SML(T)\n                           [mg/kg]\n                           (Group restriction No)",
  "Group Restriction No", "Restriction", "SML (T)\n                     [mg/kg]",
  "Verification", "Source", "Evidence", "Endpoint", "Notes",
  "FCA No", "Substance name (CN)", "Substance name (EN)",
  "Restrictions", "restrictions_and_specifications", "restriction_description",
  "source_database", "evidence_level", "endpoint", "notes", "verification"
))
