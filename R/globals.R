utils::globalVariables(c(
  ".", "CID", "value", "Hazard Statement Code(s)", "<<-",
  "FCM substance No", "Group Restriction No", "InChIKey",
  "IsomericSMILES", "NAME", "CAS", "SMILES", "iarc_meta",
  "Flavornet", "CAS_retrieved", "ExactMass", "Cramer_rules",
  "Toxic_level",
  "eu_sml_meta", "china_sml_meta", "SML", "SML_group",
  "SML\r\n                     [mg/kg]",
  "SML (T)\r\n                     [mg/kg]",
  "SML(T)\r\n                     [mg/kg]\r\n                     (Group restriction No)",
  "Formula", "ExactMass", "MolecularFormula",
  "CAS No",
  "Substance name",
  "Index No"
))
