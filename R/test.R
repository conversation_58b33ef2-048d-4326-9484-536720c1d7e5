library(labtools)
library(tidyverse)

x12326 <- rio::import("12326_cid.csv")
x12326$CID[34] <- 517894
x12326$CID[39] <- 31404
x12326 <- extract_meta(x12326)
export4toxtree(x12326, cas_col = 2, name_col = 3, "123326_for_toxtree.csv")
x12326_result <- assign_toxicity(x12326, "123326_toxtree_results.csv")
rio::export(x12326_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12326_12330/123326_high_concern_databases_search_results.xlsx") # nolint

x12327 <- rio::import("12327_cid.csv") |>
  extract_meta()
export4toxtree(x12327, cas_col = 2, name_col = 3, "123327_for_toxtree.csv")
x12327_result <- assign_toxicity(x12327, "123327_toxtree_results.csv")
rio::export(x12327_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12326_12330/123327_high_concern_databases_search_results.xlsx")

x12328 <- rio::import("12328_cid.csv") |>
  extract_meta()
export4toxtree(x12328, cas_col = 2, name_col = 3, "123328_for_toxtree.csv")
x12328_result <- assign_toxicity(x12328, "123328_toxtree_results.csv")
rio::export(x12328_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12326_12330/123328_high_concern_databases_search_results.xlsx")

x12329 <- rio::import("12329_cid.csv") |>
  extract_meta()
export4toxtree(x12329, cas_col = 2, name_col = 3, "123329_for_toxtree.csv")
x12329_result <- assign_toxicity(x12329, "123329_toxtree_results.csv")
rio::export(x12329_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12326_12330/123329_high_concern_databases_search_results.xlsx")

x12330 <- rio::import("12330_cid.csv") |>
  extract_meta()
export4toxtree(x12330, cas_col = 2, name_col = 3, "123330_for_toxtree.csv")
x12330_result <- assign_toxicity(x12330, "123330_toxtree_results.csv")
rio::export(x12330_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12326_12330/123330_high_concern_databases_search_results.xlsx")

x12901 <- rio::import("12901.xlsx")
export4toxtree(x12901, cas_col = 2, name_col = 3, "12901_for_toxtree.csv")
x12901_result <- assign_toxicity(x12901, "12901_toxtree_results.csv")
rio::export(x12901_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12901_12903/12901_in_silico_toxocity_evaluation_results.xlsx")

x12903 <- rio::import("12903.xlsx")
export4toxtree(x12903, cas_col = 2, name_col = 3, "12903_for_toxtree.csv")
x12903_result <- assign_toxicity(x12903, "12903_toxtree_results.csv")
rio::export(x12903_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12901_12903/12903_in_silico_toxocity_evaluation_results.xlsx")


library(fcmsafety)
library(tidyverse)
source("/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/project/R_packages/fcmsafety/R/databases.R", encoding = "UTF-8")
load_databases(use_default = FALSE)
x12478 <- rio::import("/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12478-12483/12478.xlsx") # nolint
x12478_result <- assign_toxicity(x12478, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12478-12483/12478_toxtree_results.csv") # nolint
rio::export(x12478_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12478-12483/12478_in_silico_toxocity_evaluation_results.xlsx")

x12483 <- rio::import("/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12478-12483/12483.xlsx") # nolint
x12483_result <- assign_toxicity(x12483, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12478-12483/12483_toxtree_results.csv") # nolint
rio::export(x12483_result, "/Users/<USER>/Library/CloudStorage/OneDrive-unizar.es/IQTC/业务/巴赛尔/GADSL/12478-12483/12483_in_silico_toxocity_evaluation_results.xlsx")


library(fcmsafety)
library(tidyverse)
load_databases(use_default = FALSE)

msms <- rio::import("/Users/<USER>/Desktop/project/2022 NQI/data/MSMS_library/NQI_MSMS_database_classyfire_20250523.xlsx")

msms_meta <- assign_toxicity(msms, "/Users/<USER>/Desktop/project/2022 NQI/data/MSMS_library/NQI_MSMS_database_toxtree_results.csv")


rio::export(msms_meta, "/Users/<USER>/Desktop/project/2022 NQI/data/MSMS_library/NQI_MSMS_database_tox_20250523.xlsx")




# section -----------------------------------------------------------
# 3158-3163
x3158 <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl-3158-3163结果汇总.xlsx", sheet = "3158") # nolint
x3158_cid <- extract_cid(x3158, cas_col = 2, name_col = 4, timeout = 200)
rio::export(x3158_cid, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3158_cid.csv") # nolint
x3158_meta <- extract_meta(x3158_cid)
rio::export(x3158_meta, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3158_meta.csv")
x3158_meta <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3158_meta.csv") # nolint
export4toxtree(x3158_meta, cas_col = 2, name_col = 3, "/Users/<USER>/Downloads/gadsl_3158_for_toxtree.csv") # nolint
x3158_result <- assign_toxicity(x3158_meta, "/Users/<USER>/Downloads/gadsl_3158_toxtree_results.csv") # nolint
rio::export(x3158_result, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/3158_in_silico_toxocity_evaluation_resultss.xlsx") # nolint

x3159 <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl-3158-3163结果汇总.xlsx", sheet = "3159") # nolint
x3159_cid <- extract_cid(x3159, cas_col = 2, name_col = 4, timeout = 200)
rio::export(x3159_cid, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3159_cid.csv") #
nolint
x3159_cid <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3159_cid.csv")
x3159_meta <- extract_meta(x3159_cid)
rio::export(x3159_meta, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3159_meta.csv")
x3159_meta <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3159_meta.csv") # nolint
export4toxtree(x3159_meta, cas_col = 2, name_col = 3, "/Users/<USER>/Downloads/gadsl_3159_for_toxtree.csv") # nolint
x3159_result <- assign_toxicity(x3159_meta, "/Users/<USER>/Downloads/gadsl_3159_toxtree_results.csv") # nolint
rio::export(x3159_result, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/3159_in_silico_toxocity_evaluation_resultss.xlsx") # nolint

x3160 <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl-3158-3163结果汇总.xlsx", sheet = "3160") # nolint
x3160_cid <- extract_cid(x3160, cas_col = 2, name_col = 4, timeout = 200)
rio::export(x3160_cid, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3160_cid.csv") # nolint
x3160_cid <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3160_cid.csv")
x3160_meta <- extract_meta(x3160_cid)
rio::export(x3160_meta, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3160_meta.csv") # nolint
x3160_meta <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3160_meta.csv") # nolint
export4toxtree(x3160_meta, cas_col = 2, name_col = 3, "/Users/<USER>/Downloads/gadsl_3160_for_toxtree.csv") # nolint
x3160_result <- assign_toxicity(x3160_meta, "/Users/<USER>/Downloads/gadsl_3160_toxtree_results.csv") # nolint
rio::export(x3160_result, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/3160_in_silico_toxocity_evaluation_resultss.xlsx") # nolint

x3161 <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl-3158-3163结果汇总.xlsx", sheet = "3161") # nolint
x3161_cid <- extract_cid(x3161, cas_col = 2, name_col = 4, timeout = 200)
rio::export(x3161_cid, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3161_cid.csv") # nolint
x3161_meta <- extract_meta(x3161_cid)
rio::export(x3161_meta, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3161_meta.csv") # nolint
x3161_meta <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3161_meta.csv") # nolint
export4toxtree(x3161_meta, cas_col = 2, name_col = 3, "/Users/<USER>/Downloads/gadsl_3161_for_toxtree.csv") # nolint
x3161_result <- assign_toxicity(x3161_meta, "/Users/<USER>/Downloads/gadsl_3161_toxtree_results.csv") # nolint
rio::export(x3161_result, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/3161_in_silico_toxocity_evaluation_resultss.xlsx") # nolint

x3162 <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl-3158-3163结果汇总.xlsx", sheet = "3162") # nolint
x3162_cid <- extract_cid(x3162, cas_col = 2, name_col = 4, timeout = 200)
rio::export(x3162_cid, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3162_cid.csv") # nolint
x3162_meta <- extract_meta(x3162_cid)
rio::export(x3162_meta, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3162_meta.csv") # nolint
x3162_meta <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3162_meta.csv") # nolint
export4toxtree(x3162_meta, cas_col = 2, name_col = 3, "/Users/<USER>/Downloads/gadsl_3162_for_toxtree.csv") # nolint
x3162_result <- assign_toxicity(x3162_meta, "/Users/<USER>/Downloads/gadsl_3162_toxtree_results.csv") # nolint
rio::export(x3162_result, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/3162_in_silico_toxocity_evaluation_resultss.xlsx") # nolint

x3163 <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl-3158-3163结果汇总.xlsx", sheet = "3163") # nolint
x3163_cid <- extract_cid(x3163, cas_col = 2, name_col = 4, timeout = 200)
rio::export(x3163_cid, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3163_cid.csv") # nolint
x3163_meta <- extract_meta(x3163_cid)
rio::export(x3163_meta, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3163_meta.csv") # nolint
x3163_meta <- rio::import("/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/gadsl_3163_meta.csv") # nolint
export4toxtree(x3163_meta, cas_col = 2, name_col = 3, "/Users/<USER>/Downloads/gadsl_3163_for_toxtree.csv") # nolint
x3163_result <- assign_toxicity(x3163_meta, "/Users/<USER>/Downloads/gadsl_3163_toxtree_results.csv") # nolint
rio::export(x3163_result, "/Users/<USER>/Desktop/IQTC/业务/巴赛尔/GADSL/3158-3163/3163_in_silico_toxocity_evaluation_resultss.xlsx") # nolint
