% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/assign_toxicity.R
\name{assign_toxicity}
\alias{assign_toxicity}
\title{Assign toxicity levels}
\usage{
assign_toxicity(data, toxtree_result = "toxtree_results.csv")
}
\arguments{
\item{data}{Your data containing at least InCh<PERSON><PERSON>ey}

\item{toxtree_result}{The *.csv file generated by Toxtree batch processing.
The defalut value is "toxtree_results.csv", which means the output files in
Toxtree batching processing is "toxtree_results.csv". If other output name is
used in Toxtree, please use the corresponding name here.}
}
\value{
A data.frame or tibble with toxicity assigned.
}
\description{
Assign toxicity levels considering SVHC, EDC, IARC, CMR, EU_10/2011, and China
GB 9685
}
