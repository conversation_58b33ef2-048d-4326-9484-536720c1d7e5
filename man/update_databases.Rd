% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/databases.R
\name{update_databases}
\alias{update_databases}
\title{Update SVHC, CMR, IARC, and EU_SML database}
\usage{
update_databases(svhc = TRUE, cmr = TRUE, iarc = TRUE, eu_sml = TRUE)
}
\arguments{
\item{svhc}{A logical value to decide whether or not to update this database.}

\item{cmr}{A logical value to decide whether or not to update this database.}

\item{iarc}{A logical value to decide whether or not to update this database.}

\item{eu_sml}{A logical value to decide whether or not to update this database.}
}
\value{
It will return updated databases with CID and meta data assigned.
}
\description{
\code{update_databases()} provides a way to update the Substances of Very High
Concern (SVHC), Carcinogenic, Mutagenic, and Reprotoxic (CMR) compounds
from the Classification, Labelling, and Packagine (CLP) regulation Table 3 of
Annex VI, Carcinogenic substances from the International Agency for Research
on Cancer (IARC), as well as the positive list of the EU 10/2011 regulation on
plastic food contact materials. It then extracts CID and meta data, e.g.,
IsomericSMILES, InChIKey. It will first check if there is a \strong{inst} folder
in the working directory. If no, it will create one and generate
some *.xlsx files that can be read in later on with the \code{load_databases()}
function. If yes, it will update existed *.xlsx files. This process can be
time-consuming (up to several hours to retrieve information from Pubchem) and
It is not necessary to update all databases every time you want to use them
because these databases will not be frequently updated by the holders. Moreover,
Chrome brower is required and it will prompt up a Chrome windows for few seconds.
Please do not close it. It will disappear after downloading the required database.
}
