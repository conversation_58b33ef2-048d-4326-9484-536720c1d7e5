% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/databases.R
\name{load_databases}
\alias{load_databases}
\title{Load all databases with meta data}
\usage{
load_databases(use_default = TRUE)
}
\arguments{
\item{use_default}{A logical value. If use_default = TRUE, it will load
the pre-prepared databases. Otherwise, it will load the use-updated databases
using the \code{update_databases()} function. In the latter case, a \strong{inst}
folder containing all the databases (*.xlsx) is required.}
}
\value{
data.frame of all databases.
}
\description{
\code{load_databases()} provides a way to load all databases with meta data
into the global environment. It requires no argument but cleaned up databases
files in *.xlsx format in the \strong{inst} folder. Pre-prepared databases
(updated on 2021/11/11) already exist in the \strong{inst} folder. If you
\code{update_databases()}, theses databases will be updated to the latest one.
At the moment, the EDC and China SML (GB 9685) databases can not be updated.
}
