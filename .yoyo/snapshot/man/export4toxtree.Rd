% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/assign_toxicity.R
\name{export4toxtree}
\alias{export4toxtree}
\title{Export input file for toxtree batch processing}
\usage{
export4toxtree(data, cas_col, name_col, output = "for_toxtree.csv")
}
\arguments{
\item{data}{Your data after the \code{extract_cid()} and \code{extract_meta()}
steps}

\item{cas_col}{The index of column that contains CAS information. CAS number
is not mandatory for each compound, if no CAS is available, then chemical name
will be used for retreival. However, a column index is still required.}

\item{name_col}{The index of column that contains chemical name.}

\item{output}{The output file name end with .csv. The default value is
"for_toxtree.csv"}
}
\value{
No return but a *.csv file for Toxtree.
}
\description{
After \code{extract_cid()} and \code{extract_meta()} for your data, you can
use this function to export a *.csv file that can be used by the Toxtree
software for batch processing.
}
