#' Export input file for toxtree batch processing
#'
#' After \code{extract_cid()} and \code{extract()} for your data, you can
#' use this function to export a *.csv file that can be used by the Toxtree
#' software for batch processing.
#'
#' @param data Your data after the \code{extract_cid()} and \code{extract()}
#' steps
#' @param cas_col The index of column that contains CAS information. CAS number
#' is not mandatory for each compound, if no CAS is available, then chemical name
#' will be used for retreival. However, a column index is still required.
#' @param name_col The index of column that contains chemical name.
#' @param output The output file name end with .csv. The default value is
#' "for_toxtree.csv"
#'
#' @return No return but a *.csv file for Toxtree.
#'
#' @import dplyr
#' @importFrom rio export
#'
#' @export
export4toxtree <-
  function(data, cas_col,
           name_col,
           output = "for_toxtree.csv") {
    data <- data %>%
      mutate(
        NAME = .[, name_col],
        CAS = .[, cas_col],
        SMILES = SMILES
      ) %>%
      select(NAME, CAS, SMILES) %>%
      filter(!is.na(SMILES))

    rio::export(data, output)
  }


#' Assign toxicity levels
#'
#' Assign toxicity levels considering SVHC, EDC, IARC, CMR, EU_10/2011, and China
#' GB 9685
#'
#' @param data Your data containing at least InChIKey
#' @param toxtree_result The *.csv file generated by Toxtree batch processing.
#' The defalut value is "toxtree_results.csv", which means the output files in
#' Toxtree batching processing is "toxtree_results.csv". If other output name is
#' used in Toxtree, please use the corresponding name here.
#'
#' @return A data.frame or tibble with toxicity assigned.
#'
#' @import dplyr
#' @importFrom utils read.csv
#'
#' @export
assign_toxicity <-
  function(data, toxtree_result = "toxtree_results.csv") {
    # read in toxtree result
    tox <- read.csv(toxtree_result)

    data <- data %>%
      mutate(
        Cramer_rules = tox$Cramer.rules[match(SMILES, tox$SMILES)],
        # to check if the compounds present in any of the database
        SVHC = case_when(InChIKey %in% svhc$InChIKey ~ "Y"),
        CMR = case_when(InChIKey %in% cmr$InChIKey ~ "Y"),
        CMR_suspect = case_when(InChIKey %in% cmr_suspect$InChIKey ~ "Y"),
        EDC = case_when(InChIKey %in% edc$InChIKey ~ "Y"),
        IARC = iarc$Group[match(InChIKey, iarc$InChIKey)] %>% na_if("3"),
        EU_SML = eu_sml$SML[match(InChIKey, eu_sml$InChIKey)],
        EU_SML_group = eu_sml$SML_group[match(InChIKey, eu_sml$InChIKey)],
        # it is possible to have both SML and SML_group
        # in this case, use only SML
        EU_SML = case_when(
          is.na(EU_SML) & !is.na(EU_SML_group)
          ~ eu_sml_group$SML[match(EU_SML_group, eu_sml_group$`Group Restriction No`)],
          TRUE ~ EU_SML
        ),
        China_SML = china_sml$SML[match(InChIKey, china_sml$InChIKey)],
        China_SML_group = china_sml$SML_group[match(InChIKey, china_sml$InChIKey)],
        China_SML = case_when(
          is.na(China_SML & !is.na(China_SML_group)) ~ China_SML_group,
          TRUE ~ China_SML
        ),
        # Cramer_rules = case_when(
        #   !is.na(SVHC) | !is.na(CMR) | !is.na(CMR_suspect) | !is.na(EDC) |
        #     !is.na(IARC) | !is.na(EU_SML) | !is.na(China_SML) ~ "-",
        #   TRUE ~ Cramer_rules
        # ),

        # assign toxiticy
        # Toxic_level = case_when(
        #   !is.na(SVHC) | !is.na(CMR) | !is.na(EDC) | IARC == "1" |
        #     EU_SML <= 0.018 | China_SML <= 0.018 ~ "V",
        #   !is.na(CMR_suspect) | IARC == "2A" | IARC == "2B" |
        #     Cramer_rules == "High (Class III)" |
        #     (EU_SML > 0.018 & EU_SML <= 0.09) | (China_SML > 0.018 & China_SML <= 0.09) ~ "IV",
        #   Cramer_rules == "Intermediate (Class II)" |
        #     (EU_SML > 0.09 & EU_SML <= 0.54) | (China_SML > 0.09 & China_SML <= 0.54) ~ "III",
        #   Cramer_rules == "Low (Class I)" |
        #     (EU_SML > 0.54 & EU_SML <= 1.8) | (China_SML > 0.54 & China_SML <= 1.8) ~ "II",
        #   (EU_SML > 1.8 & EU_SML <= 60) | (China_SML > 1.8 & China_SML <= 60) ~ "I"
        # ),

        # mark group SML
        EU_SML = case_when(
          !is.na(EU_SML_group) ~ paste0(EU_SML, "*"),
          TRUE ~ as.character(EU_SML)
        ),
        China_SML = case_when(
          !is.na(China_SML_group) ~ paste0(China_SML, "*"),
          TRUE ~ as.character(China_SML)
        ),
        EU_SML_group = NULL,
        China_SML_group = NULL
      )

    # replace NA with "-
    data[is.na(data)] <- "-"

    if ("Flavornet" %in% colnames(data)) {
      data <- relocate(data, Cramer_rules:China_SML, .after = Flavornet)
    } else if ("CAS_retrieved" %in% colnames(data)) {
      data <- relocate(data, Cramer_rules:China_SML, .after = CAS_retrieved)
    } else if ("ExactMass" %in% colnames(data)) {
      data <- relocate(data, Cramer_rules:China_SML, .after = ExactMass)
    }

    return(data)
  }
