# Helper Functions --------------------------------------------------------

#' Compare two datasets
#'
#' Helper function to compare two datasets and identify added, removed, and modified rows
#'
#' This function takes two datasets and a key column name, compares the datasets based on the key,
#' and returns a list containing keys that have been added, removed, or modified between the two datasets.
#'
#' @param old The old dataset to compare against
#' @param new The new dataset to compare
#' @param key_col The column name to use as key for comparison
#'
#' @return A list containing:
#' \itemize{
#'   \item added - vector of keys added in new dataset
#'   \item removed - vector of keys removed from old dataset
#'   \item modified - vector of keys present in both datasets but with different values
#' }
compare_datasets <- function(old, new, key_col) {
  old_keys <- old[[key_col]]
  new_keys <- new[[key_col]]

  added <- setdiff(new_keys, old_keys)
  removed <- setdiff(old_keys, new_keys)
  modified <- intersect(old_keys, new_keys)

  return(list(
    added = added,
    removed = removed,
    modified = modified
  ))
}

# Database Update Functions -----------------------------------------------

#' Update SVHC Database
#'
#' Updates the Substances of Very High Concern (SVHC) database by downloading the latest
#' candidate list from ECHA and comparing it with the existing database. Extracts CID and
#' metadata for new substances.
#'
#' @importFrom rio import export
#' @importFrom httr POST write_disk
#' @importFrom dplyr mutate left_join bind_rows filter
#' @importFrom stringr str_replace
#' @importFrom labtools extract_cid extract
update_svhc <- function() {
  # Download latest SVHC data
  url <- "https://echa.europa.eu/candidate-list-table?p_p_id=disslists_WAR_disslistsportlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_resource_id=exportResults&p_p_cacheability=cacheLevelPage" # nolint
  httr::POST(
    url,
    body = list(
      "_disslists_WAR_disslistsportlet_formDate" = as.numeric(as.POSIXct(Sys.time())) * 1000,
      "_disslists_WAR_disslistsportlet_exportColumns" = "name,ecNumber,casNumber,haz_detailed_concern,dte_inclusion,doc_cat_decision,doc_cat_iuclid_dossier,doc_cat_supdoc,doc_cat_rcom,prc_external_remarks", # nolint
      "_disslists_WAR_disslistsportlet_orderByCol" = "dte_inclusion",
      "_disslists_WAR_disslistsportlet_orderByType" = "desc",
      "_disslists_WAR_disslistsportlet_searchFormColumns" = "haz_detailed_concern,dte_inclusion",
      "_disslists_WAR_disslistsportlet_searchFormElements" = "DROP_DOWN,DATE_PICKER",
      "_disslists_WAR_disslistsportlet_substance_identifier_field_key" = "",
      "_disslists_WAR_disslistsportlet_haz_detailed_concern" = "",
      "_disslists_WAR_disslistsportlet_dte_inclusionFrom" = "",
      "_disslists_WAR_disslistsportlet_dte_inclusionTo" = "",
      "_disslists_WAR_disslistsportlet_total" = "219",
      "_disslists_WAR_disslistsportlet_exportType" = "xls"
    ),
    httr::write_disk(paste0(getwd(), "/inst/svhc_new.xlsx"), overwrite = TRUE)
  )

  # Process new data
  svhc <- rio::import(paste0(getwd(), "/inst/svhc_new.xlsx"), skip = 3)
  # load old data
  old_svhc <- rio::import(paste0(getwd(), "/inst/svhc.xlsx"))
  # remove duplidated rows to avoid error in left_join
  old_svhc <- old_svhc[!duplicated(old_svhc[, c("Substance name", "CAS No.")]), ]

  # Compare old and new data by Name
  comparison <- compare_datasets(old_svhc, svhc, "Substance name")

  # TODO: Theck any changes in the dataframe but not only the name
  # Report detailed changes
  if (length(comparison$removed) > 0) {
    message("\n- Removed substances:")
    message(paste(comparison$removed, collapse = "\n"))
  } else {
    message("No substances have been removed from the SVHC database in the latest update.")
  }
  if (length(comparison$added) > 0) {
    message("\n- Added substances:")
    message(paste(comparison$added, collapse = "\n"))
  } else {
    message("No new substances have been added to the SVHC database in the latest update.")
  }

  # making use of the meta information from the old data
  updated_svhc <- left_join(
    svhc[!svhc$`Substance name` %in% comparison$added, ],
    old_svhc[, c(1, 4, 12:17)],
    by = c("Substance name", "CAS No.")
  )

  # Only keep newly added substances in new database
  new_substances <- filter(svhc, `Substance name` %in% comparison$added)

  # Extract CID and meta for new substances
  if (nrow(new_substances) > 0) {
    message("Extracting CID and meta data for new SVHC substances:")
    new_substances <- extract_cid(new_substances, cas_col = 4, name_col = 1)
    if (any(!is.na(new_substances$CID))) {
      new_substances <- extract_meta(new_substances)
    } else {
      new_substances <- new_substances
    }

    # combing old and new information
    updated_svhc <- bind_rows(new_substances, updated_svhc)
  }

  # Save updated cmr data
  rio::export(updated_svhc, paste0(getwd(), "/inst/svhc.xlsx"))
  invisible(file.remove(paste0(getwd(), "/inst/svhc_new.xlsx")))

  message("The SVHC database has been updated.")
}

#' Update CMR Database
#'
#' Updates the Carcinogenic, Mutagenic, and Reprotoxic (CMR) substances database by
#' downloading the latest CLP regulation data from ECHA and comparing it with the
#' existing database. Extracts CID and metadata for new substances.
#'
#' @importFrom rio import export
#' @importFrom utils download.file
#' @importFrom dplyr mutate left_join bind_rows filter
#' @importFrom tidyr separate_rows
#' @importFrom stringr str_detect
#' @importFrom labtools extract_cid, extract
#' #' @import rvest
update_cmr <- function() {
  # Download latest CLP data
  url <- "https://echa.europa.eu/information-on-chemicals/annex-vi-to-clp"
  url_list <- url %>%
    read_html() %>%
    html_nodes("a") %>%
    html_attr("href") %>%
    as_tibble() %>%
    filter(str_detect(value, "documents/10162/17218/annex_vi_clp"))
  url <- paste0("https://echa.europa.eu", url_list[nrow(url_list), ])
  download.file(url, paste0(getwd(), "/inst/clp.xlsx"), quiet = TRUE, mode = "wb")

  # Process new CLP data
  clp <- suppressMessages(rio::import(paste0(getwd(), "/inst/clp.xlsx"), skip = 3))
  for (i in seq_len(ncol(clp))) {
    if (!is.na(clp[1, i])) {
      colnames(clp)[i] <- clp[1, i]
    }
  }
  clp <- clp[-1, ]

  # update CMR data# -----------------------------------------------------------
  message("Processing CMR substances:")
  # subset CMR data from the CLP data
  cmr <- filter(clp, str_detect(`Hazard Statement Code(s)`, "H340|H350|H360"))
  # load old cmr data
  old_cmr <- rio::import(paste0(getwd(), "/inst/cmr.xlsx"))
  old_cmr <- old_cmr[!duplicated(old_cmr[, c("Index No", "CAS No")]), ]

  comparison <- compare_datasets(old_cmr, cmr, "Index No")

  # Report detailed changes
  if (length(comparison$removed) > 0) {
    message("\n- Removed substances:")
    message(paste(comparison$removed, collapse = "\n"))
  } else {
    message("No substances have been removed from the CMR database in the latest update.")
  }
  if (length(comparison$added) > 0) {
    message("\n- Added substances:")
    message(paste(comparison$added, collapse = "\n"))
  } else {
    message("No new substances have been added to the CMR database in the latest update.")
  }

  updated_cmr <- left_join(
    cmr[!cmr$`Index No` %in% comparison$added, ],
    old_cmr[, c(1, 4, 13:18)],
    by = c("Index No", "CAS No")
  )

  # Only keep newly added substances in new database
  new_substances <- filter(cmr, `Index No` %in% comparison$added)

  # Extract CID and meta for new substances
  if (nrow(new_substances) > 0) {
    message("Extracting CID and meta data for new CMR substances:")
    new_substances <- new_substances |>
      mutate(`CAS No` = sub("\r\n$", "", `CAS No`)) |>
      separate_rows("CAS No", sep = "\r\n") |>
      mutate(`CAS No` = sub(" \\[.*\\]", "", `CAS No`)) |>
      extract_cid(cas_col = 4, name_col = 1)
    if (any(!is.na(new_substances$CID))) {
      new_substances <- extract_meta(new_substances)
    } else {
      new_substances <- new_substances
    }

    # Append new substances to old database
    updated_cmr <- bind_rows(new_substances, updated_cmr)
  }

  # Save new CMR data
  rio::export(updated_cmr, paste0(getwd(), "/inst/cmr.xlsx"))

  # update suspect CMR data# -----------------------------------------------------------
  message("Processing suspect CMR substances:")
  # subset suspect CMR data from the CLP data
  suspect_cmr <- filter(clp, str_detect(`Hazard Statement Code(s)`, "H341|H351|H361"))
  # load old cmr data
  old_suspect_cmr <- rio::import(paste0(getwd(), "/inst/suspect_cmr.xlsx"))
  old_suspect_cmr <- old_suspect_cmr[!duplicated(old_suspect_cmr[, c("Index No", "CAS No")]), ]

  comparison <- compare_datasets(old_suspect_cmr, suspect_cmr, "Index No")

  # Report detailed changes
  if (length(comparison$removed) > 0) {
    message("\n- Removed substances:")
    message(paste(comparison$removed, collapse = "\n"))
  } else {
    message("No substances have been removed from the suspect CMR database in the latest update.")
  }
  if (length(comparison$added) > 0) {
    message("\n- Added substances:")
    message(paste(comparison$added, collapse = "\n"))
  } else {
    message("No new substances have been added to the suspect CMR database in the latest update.")
  }

  updated_suspect_cmr <- left_join(
    suspect_cmr[!suspect_cmr$`Index No` %in% comparison$added, ],
    old_suspect_cmr[, c(1, 4, 13:18)],
    by = c("Index No", "CAS No")
  )

  # Only keep newly added substances in new database
  new_substances <- filter(suspect_cmr, `Index No` %in% comparison$added)

  # Extract CID and meta for new substances
  if (nrow(new_substances) > 0) {
    message("Extracting CID and meta data for new suspect CMR substances:")
    new_substances <- new_substances |>
      mutate(`CAS No` = sub("\r\n$", "", `CAS No`)) |>
      separate_rows("CAS No", sep = "\r\n") |>
      mutate(`CAS No` = sub(" \\[.*\\]", "", `CAS No`)) |>
      extract_cid(cas_col = 4, name_col = 1)
    if (any(!is.na(new_substances$CID))) {
      new_substances <- extract_meta(new_substances)
    } else {
      new_substances <- new_substances
    }

    # Append new substances to old database
    updated_suspect_cmr <- bind_rows(new_substances, updated_suspect_cmr)
  }

  # Save new CMR data
  rio::export(updated_suspect_cmr, paste0(getwd(), "/inst/suspect_cmr.xlsx"))
  invisible(file.remove(paste0(getwd(), "/inst/clp.xlsx")))
  message("The CMR and suspect CMR database have been updated.")
}

#' Update IARC Database
#'
#' Updates the International Agency for Research on Cancer (IARC) database by
#' downloading the latest classification list and comparing it with the existing
#' database. Extracts CID and metadata for new substances.
#'
#' @importFrom rio import export
#' @importFrom dplyr mutate left_join bind_rows
#' @importFrom RSelenium rsDriver
#' @importFrom stringr str_extract str_replace_all str_subset
#' @importFrom binman list_versions
#' @importFrom netstat free_port
#' @importFrom fs dir_ls
#' @importFrom labtools extract_cid, extract
update_iarc <- function() {
  # # the IARC database, https://monographs.iarc.who.int/agents-classified-by-the-iarc/
  # url <- "https://monographs.iarc.who.int/list-of-classifications"
  # # set the working directory for downloading
  # path <- gsub("/", "\\\\", paste0(getwd(), "/inst"))
  # # set up extra capabilities
  # ecaps <- list(chromeOptions = list(
  #   prefs = list(
  #     "profile.default_content_settings.popups" = 0L,
  #     "download.prompt_for_download" = FALSE,
  #     "download.default_directory" = path
  #   ),
  #   args = c("--disable-gpu", "--window-size=600,800")
  # ))
  # # lauch a chrome driver
  # driver <- RSelenium::rsDriver(
  #   browser = "chrome",
  #   # the long chromever avoid imcompatible chrome version
  #   # https://thatdatatho.com/tutorial-web-scraping-rselenium/
  #   chromever =
  #     system2(
  #       command = "wmic",
  #       args = 'datafile where name="C:\\\\Program Files (x86)\\\\Google\\\\Chrome\\\\Application\\\\chrome.exe" get Version /value', # nolint
  #       stdout = TRUE,
  #       stderr = TRUE
  #     ) %>%
  #       stringr::str_extract(pattern = "(?<=Version=)\\d+\\.\\d+\\.\\d+\\.") %>%
  #       magrittr::extract(!is.na(.)) %>%
  #       stringr::str_replace_all(
  #         pattern = "\\.",
  #         replacement = "\\\\."
  #       ) %>%
  #       paste0("^", .) %>%
  #       stringr::str_subset(
  #         string =
  #           binman::list_versions(appname = "chromedriver") %>%
  #             dplyr::last()
  #       ) %>%
  #       as.numeric_version() %>%
  #       max() %>%
  #       as.character(),
  #   # use free port to allowed being repeatedly executed
  #   port = netstat::free_port(),
  #   extraCapabilities = ecaps
  # )

  # remote_driver <- driver[["client"]]
  # remote_driver$navigate(url)
  # buttom_element <-
  #   remote_driver$findElement(
  #     using = "xpath",
  #     value = '//*[@id="table_wrapper"]/div[1]/button[3]'
  #   )
  # buttom_element$clickElement()
  # Sys.sleep(2)
  # remote_driver$close()

  # # get the downloaded file name
  # all_files <- fs::dir_ls(paste0(getwd(), "/inst"), glob = "*.xlsx") %>%
  #   str_remove_all(paste0(getwd(), "/inst/"))
  # file_name <- all_files[str_which(
  #   all_files,
  #   "Agents Classified by the IARC Monographs"
  # )]
  # # move the file to the desired directory
  # file.rename(
  #   file.path(paste0(getwd(), "/inst/"), file_name),
  #   file.path(paste0(getwd(), "/inst/iarc.xlsx"))
  # )
  message("In this version, manual dolwnload of the IARC database is required. Please download the latest version of the IARC database from https://monographs.iarc.who.int/list-of-classifications and place it in the inst directory") # nolint

  iarc <- rio::import(paste0(getwd(), "/inst/Agents Classified by the IARC Monographs, Volumes 1–137.xlsx"), skip = 1) # nolint

  # read in the file and extract meta
  old_iarc <- rio::import(paste0(getwd(), "/inst/iarc.xlsx"))
  old_iarc <- old_iarc[!duplicated(old_iarc[, c("CAS No.", "Agent")]), ]

  # Compare old and new data by Name
  comparison <- compare_datasets(old_iarc, iarc, "Agent")

  # Report detailed changes
  if (length(comparison$removed) > 0) {
    message("\n- Removed substances:")
    message(paste(comparison$removed, collapse = "\n"))
  } else {
    message("No substances have been removed from the IARC database in the latest update.")
  }
  if (length(comparison$added) > 0) {
    message("\n- Added substances:")
    message(paste(comparison$added, collapse = "\n"))
  } else {
    message("No new substances have been added to the IARC database in the latest update.")
  }

  updated_iarc <- left_join(
    iarc[!iarc$Agent %in% comparison$added, ],
    old_iarc[, c(1, 2, 8:13)],
    by = c("CAS No.", "Agent")
  )
  # Only keep newly added substances in new database
  new_substances <- filter(iarc, Agent %in% comparison$added)

  # Extract CID and meta for new substances
  if (nrow(new_substances) > 0) {
    message("Extracting CID and meta data for new IARC substances:")
    new_substances <- extract_cid(new_substances, cas_col = 1, name_col = 2)
    if (any(!is.na(new_substances$CID))) { # in case all CID is empty, skip the meta extraction
      new_substances <- extract_meta(new_substances)
    } else {
      new_substances <- new_substances
    }

    # Append new substances to old database
    updated_iarc <- bind_rows(new_substances, updated_iarc)
  }

  # Save updated cmr data
  rio::export(updated_iarc, paste0(getwd(), "/inst/iarc.xlsx"))
  # invisible(file.remove(paste0(getwd(), "/inst/iarc_new.xlsx")))

  message("The IARC database has been updated.")
}

#' Update EU SML Database
#'
#' Updates the EU Specific Migration Limit (SML) database by downloading the latest
#' EU 10/2011 regulation data and comparing it with the existing database. Extracts
#' CID and metadata for new substances.
#'
#' @importFrom rio import export
#' @importFrom rvest read_html html_elements html_table
#' @importFrom janitor row_to_names
#' @importFrom dplyr mutate left_join bind_rows filter distinct
#' @importFrom stringr str_detect str_remove
#' @importFrom labtools extract_cid, extract
update_eu_sml <- function() {
  # Download latest EU 10/2011 regulation data
  # url <- "https://eur-lex.europa.eu/legal-content/EN/TXT/HTML/?uri=CELEX:02011R0010-20200923&qid=1636402301680&from=en" # nolint
  url <- "https://eur-lex.europa.eu/legal-content/EN/TXT/HTML/?uri=CELEX:02011R0010-20230831&qid=1636402301680&from=en" # nolint

  # Process main SML table
  eu_nodes <- read_html(url) %>% html_elements("div.centered table tbody")
  eu_sml <- eu_nodes %>%
    .[[1]] %>%
    html_table(header = TRUE) %>%
    suppressWarnings() %>%
    janitor::row_to_names(row_number = 1) %>%
    suppressWarnings() %>%
    select(1:11) %>%
    filter(!str_detect(`FCM substance No`, "\u25bc|\\(")) %>%
    distinct(`FCM substance No`, .keep_all = TRUE) %>%
    mutate(`CAS No` = sub("^0*", "", `CAS No`))

  rio::export(eu_sml, paste0(getwd(), "/inst/eu10_2011_new.xlsx"))

  # load old data
  old_eu_sml <- rio::import(paste0(getwd(), "/inst/eu10_2011.xlsx"))
  old_eu_sml <- old_eu_sml[!duplicated(old_eu_sml[, c("FCM substance No", "CAS No")]), ]

  # Compare old and new data by Name
  comparison <- compare_datasets(old_eu_sml, eu_sml, "FCM substance No")

  # Report detailed changes
  if (length(comparison$removed) > 0) {
    message("\n- Removed substances:")
    message(paste(comparison$removed, collapse = "\n"))
  } else {
    message("No substances have been removed from EU10 2011 in the latest update.")
  }
  if (length(comparison$added) > 0) {
    message("\n- Added substances:")
    message(paste(comparison$added, collapse = "\n"))
  } else {
    message("No new substances have been added to EU10 2011 in the latest update.")
  }

  updated_eu_sml <- left_join(
    eu_sml[!eu_sml$`FCM substance No` %in% comparison$added, ],
    old_eu_sml[, c(1, 3, 12:17)],
    by = c("FCM substance No", "CAS No")
  )
  # Only keep newly added substances in new database
  new_substances <- filter(eu_sml, `FCM substance No` %in% comparison$added)

  # Extract CID and meta for new substances
  if (nrow(new_substances) > 0) {
    message("Extracting CID and meta data for new EU10 2011 substances:")
    new_substances <- extract_cid(new_substances, cas_col = 3, name_col = 4)
    if (any(!is.na(new_substances$CID))) { # in case all CID is empty, skip the meta extraction
      new_substances <- extract_meta(new_substances)
    } else {
      new_substances <- new_substances
    }

    # Append new substances to old database
    updated_eu_sml <- bind_rows(new_substances, updated_eu_sml)
  }

  # Save updated cmr data
  rio::export(updated_eu_sml, paste0(getwd(), "/inst/eu10_2011.xlsx"))
  invisible(file.remove(paste0(getwd(), "/inst/eu10_2011_new.xlsx")))

  # Process group SML table
  eu_sml_group <- eu_nodes %>%
    .[[2]] %>%
    html_table(header = TRUE) %>%
    suppressWarnings() %>%
    janitor::row_to_names(row_number = 1) %>%
    select(1:4) %>%
    filter(!str_detect(`FCM substance No`, "\u25bc")) %>%
    distinct(`Group Restriction No`, .keep_all = TRUE)
  rio::export(eu_sml_group, paste0(getwd(), "/inst/eu10_2011_group.xlsx"))
}

update_cn_sml <- function() {
  # Download latest EU 10/2011 regulation data
  cn_sml <- rio::import("GB 9685 添加剂清单-20241220.xlsx")

  # load old data
  old_cn_sml <- rio::import(paste0(getwd(), "/inst/china_sml_cleaned.xlsx"))
  old_cn_sml <- old_cn_sml[!duplicated(old_cn_sml[, c("FCA No", "CAS")]), ]

  # Compare old and new data by Name
  comparison <- compare_datasets(old_cn_sml, cn_sml, "FCA No")

  # Report detailed changes
  if (length(comparison$removed) > 0) {
    message("\n- Removed substances:")
    message(paste(comparison$removed, collapse = "\n"))
  } else {
    message("No substances have been removed from EU10 2011 in the latest update.")
  }
  if (length(comparison$added) > 0) {
    message("\n- Added substances:")
    message(paste(comparison$added, collapse = "\n"))
  } else {
    message("No new substances have been added to EU10 2011 in the latest update.")
  }

# update china sml data
  updated_cn_sml <- left_join(
    cn_sml[!cn_sml$`FCA No` %in% comparison$added, ],
    old_cn_sml[, c(2, 3, 12:17)],
    by = c("FCA No", "CAS")
  )
  # Only keep newly added substances in new database
  new_substances <- filter(cn_sml, `FCA No` %in% comparison$added)

  # Extract CID and meta for new substances
  if (nrow(new_substances) > 0) {
    message("Extracting CID and meta data for new GB9685 substances:")
    new_substances <- extract_cid(new_substances, cas_col = 3, name_col = 5)
    if (any(!is.na(new_substances$CID))) { # in case all CID is empty, skip the meta extraction
      new_substances <- extract_meta(new_substances)
    } else {
      new_substances <- new_substances
    }

    # Append new substances to old database
    updated_cn_sml <- bind_rows(new_substances, updated_cn_sml)
  }


  # Save updated cmr data
  rio::export(updated_cn_sml, paste0(getwd(), "/inst/china_sml_cleaned.xlsx"))
  # invisible(file.remove(paste0(getwd(), "/inst/eu10_2011_new.xlsx")))
}

#' Update Chemical Safety Databases
#'
#' Updates multiple chemical safety databases in parallel, including SVHC, CMR,
#' IARC, and EU SML databases. Provides detailed change tracking and error handling.
#'
#' @param svhc Logical indicating whether to update SVHC database (default: TRUE)
#' @param cmr Logical indicating whether to update CMR database (default: TRUE)
#' @param iarc Logical indicating whether to update IARC database (default: TRUE)
#' @param eu_sml Logical indicating whether to update EU SML database (default: TRUE)
#'
#' @return A list containing detailed change tracking information for each database
#' @export
#'
#' @examples
#' \dontrun{
#' # Update all databases
#' changes <- update_databases()
#' }
update_databases <- function(svhc = TRUE, cmr = TRUE, iarc = TRUE, eu_sml = TRUE) {
  # Create inst directory if it doesn't exist
  if (!dir.exists(paste0(getwd(), "/inst"))) {
    dir.create(paste0(getwd(), "/inst"))
  }

  # Update SVHC database
  if (svhc) {
    message("Updating SVHC database...")
    tryCatch(
      {
        update_svhc()
      },
      error = function(e) {
        message("Error updating SVHC database:")
        message(e)
      }
    )
  }

  # Update CMR database
  if (cmr) {
    message("Updating CMR database...")
    tryCatch(
      {
        update_cmr()
      },
      error = function(e) {
        message("Error updating CMR database:")
        message(e)
      }
    )
  }

  # Update IARC database
  if (iarc) {
    message("Updating IARC database...")
    tryCatch(
      {
        update_iarc()
      },
      error = function(e) {
        message("Error updating IARC database:")
        message(e)
      }
    )
  }

  # Update EU SML database
  if (eu_sml) {
    message("Updating EU SML database...")
    tryCatch(
      {
        update_eu_sml()
      },
      error = function(e) {
        message("Error updating EU SML database:")
        message(e)
      }
    )
  }
}

#' Load all databases with meta data
#'
#' \code{load_databases()} provides a way to load all databases with meta data
#' into the global environment. It requires no argument but cleaned up databases
#' files in *.xlsx format in the \strong{inst} folder. Pre-prepared databases
#' (updated on 2021/11/11) already exist in the \strong{inst} folder. If you
#' \code{update_databases()}, theses databases will be updated to the latest one.
#' At the moment, the EDC and China SML (GB 9685) databases can not be updated.
#'
#' @param use_default A logical value. If use_default = TRUE, it will load
#' the pre-prepared databases. Otherwise, it will load the use-updated databases
#' using the \code{update_databases()} function. In the latter case, a \strong{inst}
#' folder containing all the databases (*.xlsx) is required.
#'
#' @return data.frame of all databases.
#'
#' @export
#'
#' @importFrom rio import export
load_databases <- function(use_default = TRUE) {
  if (use_default == FALSE) {
    svhc <- rio::import(paste0(getwd(), "/inst/svhc.xlsx"))
    cmr <- rio::import(paste0(getwd(), "/inst/cmr.xlsx"))
    cmr_suspect <- rio::import(paste0(getwd(), "/inst/suspect_cmr.xlsx"))
    iarc <- rio::import(paste0(getwd(), "/inst/iarc.xlsx"))
    eu_sml <- rio::import(paste0(getwd(), "/inst/eu10_2011.xlsx"))
    eu_sml_group <- rio::import(paste0(getwd(), "/inst/eu10_2011_group.xlsx"))
    edc <- rio::import(paste0(getwd(), "/inst/edc.xlsx"))
    china_sml <- suppressWarnings(rio::import(paste0(getwd(), "/inst/china_sml_cleaned.xlsx")))
  } else {
    svhc <- rio::import(system.file("svhc.xlsx", package = "fcmsafety"))
    cmr <- rio::import(system.file("cmr.xlsx", package = "fcmsafety"))
    cmr_suspect <- rio::import(
      system.file("suspect_cmr.xlsx",
        package = "fcmsafety"
      )
    )
    iarc <- rio::import(system.file("iarc.xlsx", package = "fcmsafety"))
    eu_sml <- rio::import(system.file("eu10_2011.xlsx", package = "fcmsafety"))
    eu_sml_group <- rio::import(system.file("eu10_2011.xlsx", package = "fcmsafety"))
    edc <- rio::import(system.file("edc.xlsx", package = "fcmsafety"))
    china_sml <- rio::import(system.file("china_sml_cleaned.xlsx",
      package = "fcmsafety"
    )) %>%
      suppressWarnings()
  }

  # read in the glogal environment and clean them up
  svhc <<- svhc %>% filter(!is.na(InChIKey))
  cmr <<- cmr %>% filter(!is.na(InChIKey))
  cmr_suspect <<- cmr_suspect %>% filter(!is.na(InChIKey))
  iarc <<- iarc %>% filter(!is.na(InChIKey))
  eu_sml <<- eu_sml %>%
    filter(!is.na(InChIKey)) %>%
    rename(
      SML = `SML\n                           [mg/kg]`,
      SML_group = `SML(T)\n                           [mg/kg]\n                           (Group restriction No)` # nolint
    ) %>%
    mutate(
      SML = SML %>%
        str_replace(",", ".") %>%
        str_replace("ND", "0.01") %>%
        # soybean oil expoxidized have 2 SML, keep only the first one
        str_remove_all("\n.*$") %>%
        trimws() %>%
        as.numeric(),
      SML_group = str_remove_all(SML_group, "\\(|\\)")
    )
  eu_sml_group <<- eu_sml_group %>%
    rename(SML = `SML (T)\n                     [mg/kg]`) %>%
    mutate(SML = SML %>%
      str_replace(",", ".") %>%
      str_replace("ND", "0.01") %>%
      trimws() %>%
      as.numeric())
  edc <<- edc %>% filter(!is.na(InChIKey))
  china_sml <<- china_sml
}
