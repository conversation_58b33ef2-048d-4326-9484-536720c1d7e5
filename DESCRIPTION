Package: fcmsafety
Title: Evaluate Safety of Compounds from Food Contact Materials
Version: 0.1.6
Authors@R: 
    person("Qizhi", "<PERSON>", email = "<EMAIL>", role = c("aut", "cre"),
           comment = c(ORCID = "https://orcid.org/0000-0002-8124-997X"))
Description: The main function of this package is to evaluate the safety of 
    compounds from food contact materials. It takes into account the toxicity
    data from ECHA's Substances of Very High Concern (SVHC), Carcinogenic, Mutagenic, 
    and Reprotoxic (CMR) from the Classification, Labelling, and Packaging (CLP)
    regulation, Carcinogenic substances from IARC, Endocrine Disrupting Chemicals (EDC)
    from The International Panel on Chemical Pollution (IPCP) commissioned by UN 
    Environment, Specific Migration Limit (SML) from both EU 10/2011 and China GB 9685
    regulation. In addition, flavonet and classifire information retrieval are integrated
    as well.
License: MIT + file LICENSE
Encoding: UTF-8
Roxygen: list(markdown = TRUE)
RoxygenNote: 7.3.1
Imports: 
    binman,
    dplyr,
    fs,
    httr,
    janitor,
    labtools,
    magrittr,
    netstat,
    rio,
    RSelenium,
    rvest,
    stringr,
    webchem
URL: https://github.com/QizhiSu/fcmsafety
BugReports: https://github.com/QizhiSu/fcmsafety/issues
Remotes: 
    QizhiSu/labtools
